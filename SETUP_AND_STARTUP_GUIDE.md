# Pricing Engine - Setup and Startup Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Configuration](#database-configuration)
4. [Starting the Application](#starting-the-application)
5. [Verification](#verification)
6. [Troubleshooting](#troubleshooting)
7. [API Documentation](#api-documentation)
8. [Development Notes](#development-notes)

## Prerequisites

### Required Software
- **Python 3.11.x** (Required for dependency compatibility)
  - Python 3.13+ is NOT supported due to PyTorch compatibility issues
  - Python 3.10+ should work but 3.11 is recommended
- **PostgreSQL** (for production database)
- **Redis** (for HTS2Duty service caching)
- **Git LFS** (for large files in the repository)

### System Requirements
- macOS, Linux, or Windows
- At least 4GB RAM (8GB+ recommended for ML models)
- 2GB+ free disk space

### Check Python Version
```bash
python3.11 --version
# Should output: Python 3.11.x
```

If Python 3.11 is not available, install it:
- **macOS**: `brew install python@3.11`
- **Ubuntu/Debian**: `sudo apt-get install python3.11 python3.11-venv`
- **Windows**: Download from [python.org](https://www.python.org/downloads/)

## Environment Setup

### 1. Clone and Navigate to Repository
```bash
cd /path/to/your/workspace
git clone <repository-url>
cd pricing-engine
```

### 2. Install Git LFS (if not already installed)
```bash
# macOS
brew install git-lfs

# Ubuntu/Debian
sudo apt-get install git-lfs

# Initialize and pull large files
git lfs install
git lfs pull
```

### 3. Create Virtual Environment
```bash
# Remove any existing environment
rm -rf pricing-engine-env

# Create new environment with Python 3.11
python3.11 -m venv pricing-engine-env

# Activate the environment
source pricing-engine-env/bin/activate

# Upgrade pip
pip install --upgrade pip
```

### 4. Install Dependencies
```bash
# Install core Flask dependencies first
pip install flask flask-cors flask-migrate flask-restx flask-sqlalchemy gunicorn psycopg2-binary python-dotenv alembic prometheus-flask-exporter

# Install LangChain and AI dependencies
pip install langchain langchain-openai langchain-core langchain-community

# Install ML dependencies
pip install torch torchvision torchaudio
pip install sentence-transformers scikit-learn pandas boto3

# Install additional dependencies
pip install beautifulsoup4 lxml redis psutil
```

**Note**: If you encounter dependency conflicts, install packages in the order shown above.

## Database Configuration

### 1. Environment Variables
Ensure your `.env` file contains the correct database configuration:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database_name
DB_HOST=your-db-host
DB_NAME=your-db-name
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_PORT=5432

# API Keys
OPENAI_API_KEY=your-openai-api-key
PERPLEXITY_API_KEY=your-perplexity-api-key

# HTS2Duty Configuration
USE_LOCAL_HTS2DUTY=true
HTS2DUTY_SERVICE_URL=http://localhost:8080

# Other Configuration
FLASK_ENV=development
```

### 2. Run Database Migrations
```bash
# Activate virtual environment
source pricing-engine-env/bin/activate

# Run migrations
flask db upgrade
```

**Expected Output:**
```
INFO  [alembic.runtime.migration] Context impl PostgresqlImpl.
INFO  [alembic.runtime.migration] Will assume transactional DDL.
```

## Starting the Application

The application consists of two services that need to be started in order:

### 1. Start HTS2Duty Service (Port 8080)
```bash
# Terminal 1: Start HTS2Duty service
cd hts2duty
source ../pricing-engine-env/bin/activate
python api.py
```

**Expected Output:**
```
Redis flushed successfully
Loading pre-computed indices...
 * Serving Flask app 'api'
 * Running on http://127.0.0.1:8080
```

### 2. Start Main Pricing Engine (Port 5001)
```bash
# Terminal 2: Start main application
cd /path/to/pricing-engine
source pricing-engine-env/bin/activate
python main.py
```

**Expected Output:**
```
Invoking Duty Calculator
Loading pre-computed indices...
[INFO] Loaded historical data with 170 records
 * Serving Flask app 'app'
 * Debug mode: on
 * Running on http://127.0.0.1:5001
```

### 3. Alternative: Using Screen/Tmux (Single Terminal)
```bash
# Start HTS2Duty in background
cd hts2duty
source ../pricing-engine-env/bin/activate
nohup python api.py > hts2duty.log 2>&1 &

# Start main application
cd ..
python main.py
```

## Verification

### 1. Check Service Status
```bash
# Check if services are running
curl http://localhost:8080/tariff?hs_code=**********&country=India
curl http://localhost:5001/pricing/health
```

### 2. Access API Documentation
Open in your browser:
- **Main API Documentation**: http://localhost:5001/pricing/docs
- **Health Check**: http://localhost:5001/pricing/health

### 3. Verify Logs
Check that both services show:
- ✅ Database connection successful
- ✅ Pre-computed indices loaded
- ✅ Historical data loaded
- ✅ No error messages

## Troubleshooting

### Common Issues and Solutions

#### 1. Python Version Issues
**Error**: `ModuleNotFoundError` or dependency conflicts
**Solution**: Ensure you're using Python 3.11:
```bash
python3.11 --version
rm -rf pricing-engine-env
python3.11 -m venv pricing-engine-env
```

#### 2. Port Already in Use
**Error**: `Address already in use` on port 8080 or 5001
**Solution**: 
```bash
# Find and kill process using the port
lsof -i :8080
kill <PID>

# Or use different ports
export PORT=8081  # for HTS2Duty
python main.py --port 5002  # for main app
```

#### 3. Database Connection Issues
**Error**: Database connection failed
**Solution**:
- Verify `.env` file configuration
- Check database server is running
- Test connection: `psql -h host -U user -d database`

#### 4. Missing Dependencies
**Error**: `ModuleNotFoundError: No module named 'xyz'`
**Solution**:
```bash
source pricing-engine-env/bin/activate
pip install <missing-package>
```

#### 5. Redis Connection Issues (HTS2Duty)
**Error**: Redis connection failed
**Solution**:
```bash
# Install and start Redis
brew install redis  # macOS
sudo apt-get install redis-server  # Ubuntu

# Start Redis
redis-server
```

#### 6. Large File Issues
**Error**: Missing data files or indices
**Solution**:
```bash
git lfs install
git lfs pull
```

### Performance Issues

#### 1. Slow Startup
- **Cause**: Loading ML models and indices
- **Solution**: Normal behavior, wait 30-60 seconds for full startup

#### 2. High Memory Usage
- **Cause**: ML models (PyTorch, sentence-transformers)
- **Solution**: Ensure at least 4GB RAM available

## API Documentation

### Main Endpoints
- **Chat**: `POST /pricing/chat` - AI-powered pricing queries
- **Code Resolution**: `POST /pricing/resolve-code` - HS code resolution
- **Trade Finder**: `POST /pricing/find-trades` - Find trade data
- **Trade Ranker**: `POST /pricing/rank-trades` - Rank trade opportunities
- **Health**: `GET /pricing/health` - Service health check

### HTS2Duty Endpoints
- **Tariff Lookup**: `GET /tariff?hs_code=<code>&country=<country>`

### Authentication
Currently, the API does not require authentication for development use.

## Development Notes

### File Structure
```
pricing-engine/
├── app/                  # Main Flask application
│   ├── controllers/      # API endpoints
│   ├── services/         # Business logic
│   ├── models/           # Database models
│   └── utils/            # Utility functions
├── hts2duty/             # HTS2Duty service
├── migrations/           # Database migrations
├── resources/            # Data files and indices
├── requirements.txt      # Python dependencies
├── main.py               # Application entry point
└── .env                  # Environment configuration
```

### Key Configuration Files
- **`.env`**: Environment variables and API keys
- **`requirements.txt`**: Python package dependencies
- **`main.py`**: Flask application entry point
- **`hts2duty/api.py`**: HTS2Duty service entry point

### Development vs Production
- **Development**: Uses Flask development server (current setup)
- **Production**: Use Gunicorn WSGI server (see README.md)

### Monitoring
- **Metrics**: Available at http://localhost:5001/metrics
- **Logs**: Check terminal output or log files
- **Health**: http://localhost:5001/pricing/health

---

## Quick Start Commands

```bash
# 1. Setup (one-time)
python3.11 -m venv pricing-engine-env
source pricing-engine-env/bin/activate
pip install --upgrade pip
pip install flask flask-cors flask-migrate flask-restx flask-sqlalchemy gunicorn psycopg2-binary python-dotenv alembic prometheus-flask-exporter
pip install langchain langchain-openai langchain-core langchain-community
pip install torch torchvision torchaudio sentence-transformers scikit-learn pandas boto3
pip install beautifulsoup4 lxml redis psutil
flask db upgrade

# 2. Start Services (every time)
# Terminal 1:
cd hts2duty && source ../pricing-engine-env/bin/activate && python api.py

# Terminal 2:
source pricing-engine-env/bin/activate && python main.py

# 3. Access
# API Docs: http://localhost:5001/pricing/docs
# Health: http://localhost:5001/pricing/health
```

## Production Deployment

### Using Docker
```bash
# Build the image
docker build -t pricing-engine .

# Run the container
docker run -p 8080:8080 \
  -e DATABASE_URL=********************************/db \
  -e OPENAI_API_KEY=your-key \
  pricing-engine
```

### Using Gunicorn
```bash
# Install gunicorn (already included in requirements)
source pricing-engine-env/bin/activate

# Start with gunicorn
gunicorn -c gunicorn_config.py app:server
```

### Environment Variables for Production
```env
FLASK_ENV=production
DATABASE_URL=***********************************************/prod-db
USE_LOCAL_HTS2DUTY=false
HTS2DUTY_SERVICE_URL=https://your-hts2duty-service.com
```

## Backup and Maintenance

### Database Backup
```bash
# Backup database
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Restore database
psql $DATABASE_URL < backup_20250528.sql
```

### Log Rotation
```bash
# For production, set up log rotation
sudo logrotate -f /etc/logrotate.d/pricing-engine
```

### Updates
```bash
# Update dependencies
source pricing-engine-env/bin/activate
pip install --upgrade -r requirements.txt

# Run new migrations
flask db upgrade
```

---

## Support and Contact

- **Documentation**: This file and README.md
- **API Documentation**: http://localhost:5001/pricing/docs
- **Issues**: Check logs and troubleshooting section above
- **Performance**: Monitor at http://localhost:5001/metrics

---

**Last Updated**: May 28, 2025
**Python Version**: 3.11.x
**Status**: ✅ Tested and Working
**Author**: Augment Agent
